#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文斷詞工具
使用 CKIP Lab 的 ckip-transformers 套件進行中文斷詞、詞性標記和實體辨識

安裝方式：
pip install ckip-transformers

作者：先傑電腦
日期：2025-06-30
"""

from ckip_transformers.nlp import CkipWordSegmenter, CkipPosTagger, CkipNerChunker
import time


class ChineseNLPProcessor:
    """中文自然語言處理器"""
    
    def __init__(self, model_size="bert-base", device=-1):
        """
        初始化中文自然語言處理器
        
        Args:
            model_size (str): 模型大小，可選 "bert-tiny", "bert-base", "albert-tiny", "albert-base"
            device (int): 設備ID，-1 為 CPU，0 為 GPU:0
        """
        self.model_size = model_size
        self.device = device
        
        print(f"正在初始化模型 ({model_size})...")
        print("這可能需要一些時間來下載模型...")
        
        # 初始化斷詞器
        print("初始化斷詞器...")
        self.ws_driver = CkipWordSegmenter(model=model_size, device=device)
        
        # 初始化詞性標記器
        print("初始化詞性標記器...")
        self.pos_driver = CkipPosTagger(model=model_size, device=device)
        
        # 初始化實體辨識器
        print("初始化實體辨識器...")
        self.ner_driver = CkipNerChunker(model=model_size, device=device)
        
        print("模型初始化完成！\n")
    
    def word_segmentation(self, texts, use_delim=False, batch_size=32, max_length=512):
        """
        中文斷詞
        
        Args:
            texts (list): 要處理的文本列表
            use_delim (bool): 是否使用分隔符自動切句
            batch_size (int): 批次大小
            max_length (int): 最大句子長度
            
        Returns:
            list: 斷詞結果
        """
        if isinstance(texts, str):
            texts = [texts]
        
        return self.ws_driver(texts, use_delim=use_delim, batch_size=batch_size, max_length=max_length)
    
    def pos_tagging(self, word_segments, use_delim=True, batch_size=32, max_length=512):
        """
        詞性標記
        
        Args:
            word_segments (list): 斷詞結果
            use_delim (bool): 是否使用分隔符自動切句
            batch_size (int): 批次大小
            max_length (int): 最大句子長度
            
        Returns:
            list: 詞性標記結果
        """
        return self.pos_driver(word_segments, use_delim=use_delim, batch_size=batch_size, max_length=max_length)
    
    def named_entity_recognition(self, texts, use_delim=False, batch_size=32, max_length=512):
        """
        實體辨識
        
        Args:
            texts (list): 要處理的文本列表
            use_delim (bool): 是否使用分隔符自動切句
            batch_size (int): 批次大小
            max_length (int): 最大句子長度
            
        Returns:
            list: 實體辨識結果
        """
        if isinstance(texts, str):
            texts = [texts]
        
        return self.ner_driver(texts, use_delim=use_delim, batch_size=batch_size, max_length=max_length)
    
    def full_pipeline(self, texts, use_delim=False, batch_size=32, max_length=512):
        """
        完整的NLP處理流程：斷詞 + 詞性標記 + 實體辨識
        
        Args:
            texts (list): 要處理的文本列表
            use_delim (bool): 是否使用分隔符自動切句
            batch_size (int): 批次大小
            max_length (int): 最大句子長度
            
        Returns:
            dict: 包含所有處理結果的字典
        """
        if isinstance(texts, str):
            texts = [texts]
        
        print("執行斷詞...")
        ws_results = self.word_segmentation(texts, use_delim, batch_size, max_length)
        
        print("執行詞性標記...")
        pos_results = self.pos_tagging(ws_results, use_delim, batch_size, max_length)
        
        print("執行實體辨識...")
        ner_results = self.named_entity_recognition(texts, use_delim, batch_size, max_length)
        
        return {
            'original_texts': texts,
            'word_segmentation': ws_results,
            'pos_tagging': pos_results,
            'named_entities': ner_results
        }
    
    @staticmethod
    def format_ws_pos_result(words, pos_tags):
        """
        格式化斷詞和詞性標記結果
        
        Args:
            words (list): 斷詞結果
            pos_tags (list): 詞性標記結果
            
        Returns:
            str: 格式化後的結果
        """
        assert len(words) == len(pos_tags), "斷詞和詞性標記結果長度不一致"
        
        formatted_words = []
        for word, pos in zip(words, pos_tags):
            formatted_words.append(f"{word}({pos})")
        
        return "　".join(formatted_words)
    
    @staticmethod
    def print_results(results):
        """
        美化輸出處理結果
        
        Args:
            results (dict): full_pipeline 的返回結果
        """
        texts = results['original_texts']
        ws_results = results['word_segmentation']
        pos_results = results['pos_tagging']
        ner_results = results['named_entities']
        
        for i, (text, ws, pos, ner) in enumerate(zip(texts, ws_results, pos_results, ner_results)):
            print(f"=== 句子 {i+1} ===")
            print(f"原文：{text}")
            print(f"斷詞：{' / '.join(ws)}")
            print(f"詞性：{ChineseNLPProcessor.format_ws_pos_result(ws, pos)}")
            
            if ner:
                print("實體辨識：")
                for entity in ner:
                    print(f"  - {entity.word} ({entity.ner}) 位置: {entity.idx}")
            else:
                print("實體辨識：無實體")
            
            print()


def demo():
    """示範程式"""
    print("=== CKIP Lab 中文斷詞工具示範 ===\n")
    
    # 初始化處理器（使用較小的模型以節省記憶體）
    processor = ChineseNLPProcessor(model_size="bert-tiny", device=-1)
    
    # 測試文本
    test_texts = [
        "傅達仁今將執行安樂死，卻突然爆出自己20年前遭緯來體育台封殺，他不懂自己哪裡得罪到電視台。",
        "美國參議院針對今天總統布什所提名的勞工部長趙小蘭展開認可聽證會，預料她將會很順利通過參議院支持，成為該國有史以來第一位的華裔女性內閣成員。",
        "台灣是一個美麗的島嶼，擁有豐富的自然資源和多元的文化。",
        "人工智慧技術在近年來發展迅速，深度學習已經成為機器學習的重要分支。"
    ]
    
    # 執行完整的NLP處理流程
    start_time = time.time()
    results = processor.full_pipeline(test_texts)
    end_time = time.time()
    
    # 輸出結果
    processor.print_results(results)
    
    print(f"處理時間：{end_time - start_time:.2f} 秒")
    print(f"處理了 {len(test_texts)} 個句子")


def interactive_mode():
    """互動模式"""
    print("=== 互動模式 ===")
    print("輸入 'quit' 或 'exit' 結束程式")
    print("輸入 'demo' 執行示範")
    print()
    
    processor = None
    
    while True:
        user_input = input("請輸入要處理的中文文本：").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("再見！")
            break
        
        if user_input.lower() == 'demo':
            demo()
            continue
        
        if not user_input:
            print("請輸入有效的文本")
            continue
        
        # 延遲初始化處理器
        if processor is None:
            print("首次使用，正在初始化模型...")
            processor = ChineseNLPProcessor(model_size="bert-tiny", device=-1)
        
        try:
            # 處理用戶輸入
            results = processor.full_pipeline([user_input])
            processor.print_results(results)
        
        except Exception as e:
            print(f"處理時發生錯誤：{e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo()
    elif len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_mode()
    else:
        print("使用方式：")
        print("python chinese_word_segmentation.py demo        # 執行示範")
        print("python chinese_word_segmentation.py interactive # 互動模式")
        print()
        print("或者直接執行示範：")
        demo()
